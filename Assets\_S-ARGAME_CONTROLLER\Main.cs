using System;
using System.Collections;
using System.Collections.Generic;
using System.Threading.Tasks;
using Tabula.Licensing.LicenseActivator;
using Tabula.Unity;
using Tabula.PMCore.Unity;
using Tabula.SharedObjectMap;
using UnityEngine;
using DG.Tweening;
using UnityEngine.UI;
using System.IO;
using Tabula.WebServices.Arguments;
using Tabula.PMCore;
using Tabula.P2P;
using System.Linq;
using static Tabula.PWG.MobileController.MobileController;

namespace Tabula.PWG.MobileController
{
    public class Main : MonoBehaviour
    {
		public string AuthCode;
		public string ClientID;

		public enum P2PStates
		{
			None = 0,
			Connecting =1,
			Ready = 2,

			Failed = -1
		}

		[Header("Networking")]
		private NetworkDiscovery	NetworkDiscoveryClient;
		private Tabula.P2P.P2P.ClientData      p2p_client_data;

		// P2P Instance Management
		private Tabula.P2P.P2P _p2pInstance;
		private Tabula.P2P.P2P P2PInstance
		{
			get
			{
				if (_p2pInstance == null)
					_p2pInstance = new Tabula.P2P.P2P();
				return _p2pInstance;
			}
		}
		public float				DiscoveryInterval = 3;
		public float				DiscoveryWaitAfterBroadcast = 1.5f;
		public float				MaxJoinRetryTime = 10f;
		private float				time_last_discovery = float.MinValue;

		public P2PStates			P2PStatus = P2PStates.None;
		public float				P2PConnectionTimeout = 20;

		public string				local_gameserver_address = null;
		public int					local_gameserver_port = 0;

		public string				p2p_gameserver_address = null;
		public int					p2p_gameserver_port = 0;

		public bool					ForceLocalAddress = false;
		public string				GameServerAddress
		{
			get
			{
				if (ForceLocalAddress)
				{
					if (IsCustomServerAddressEnabled)
						return CustomServerAddress;
					else if (local_gameserver_address != null)
						return local_gameserver_address;
					else
						return null; // ERROR
				}
				else
				{
					// normal priority to p2p
					return !string.IsNullOrEmpty(p2p_gameserver_address) ? p2p_gameserver_address : local_gameserver_address;
				}
			}
		}
			
		public int					GameServerPort
		{
			get
			{
				if (ForceLocalAddress)
				{
					if (IsCustomServerAddressEnabled)
						return 16100;
					else
						return local_gameserver_port;
				}
				else
				{
					// normal priority to p2p
					return p2p_gameserver_port != 0 ? p2p_gameserver_port : local_gameserver_port;
				}
			}
		}

		public bool					IsCustomServerAddressEnabled => PlayerPrefs.GetInt(InputSettings.CUSTOM_SERVER_ADDRESS_ENABLED, 0) == 1 && !string.IsNullOrEmpty(PlayerPrefs.GetString(InputSettings.CUSTOM_SERVER_ADDRESS, null));
		public string				CustomServerAddress => PlayerPrefs.GetString(InputSettings.CUSTOM_SERVER_ADDRESS, "");



		private NetworkDiscovery.DiscoveryInfo _gameserver_discovery;

		[Header("Global buttons")]
		public Button _bt_back;

		[Header("Login")]
		public CanvasGroup	content_login;
		public Button		_bt_login;				// appears only if athcode is already set
		public Button		_bt_login_clear;		// appears only if athcode is already set
		public CanvasGroup	auth_code_pad;

		[Header("Calibration")]
		public CanvasGroup	content_calibration;
		public Button		_bt_choice_calibrate;
		public CameraModes	CameraMode = Main.CameraModes.Unity;
		public CanvasGroup  calibrate_unity_content;				// other UI layer used only in Unity camera mode (native mode calls native photo app)
		public Button		_bt_calibrate_unity_confirm, _bt_calibrate_unity_abort;
		private WebCamTexture webcamTexture;
		public RawImage		ui_camera_image;
		public int			image_width = 1600;

		[Header("Gamepad")]
		public CanvasGroup			content_controller;
		public float				content_controller_target_alpha = 1;	// the target alpha when visible
		public MobileController		mobile_controller;
		public CanvasGroup			gyro_controller;
        public CanvasGroup			stick_controller;
        public CanvasGroup			touch_controller;
        public Button				_bt_mobile_controller_disconnect;       

		[Header("Icons")]
		public Sprite		icon_progress_standard;
		public Sprite		icon_ok;
		public Sprite		icon_error;
		public Sprite		icon_progress_calibration;
		public Sprite		icon_connect_wait;
		public Sprite		icon_settings_default;
		public Sprite       icon_settings_close;

		private Resolution webcam_resolution;

		[Header("Sounds")]
		public AudioClip	sound_opening;
		public AudioClip	sound_button_press;
		public AudioClip	sound_ok;
		public AudioClip	sound_error;
		public AudioClip	sound_progress;     // upload..

		[Header("Dialogs")]
		public MessageBox				msgbox;
		public Tabula.Unity.Progress	progress;
		public Tabula.Unity.Progress	wait_queued;	// for joined players that cannot play now TODO
		public IconDialog				dialog_icon;

		[Header("Icon Dialog Configs")]
		public IconDialog.Config dialog_config_kill;
		public IconDialog.Config dialog_config_gamepad;
		public IconDialog.Config dialog_config_connect_fail;
		public IconDialog.Config dialog_config_connect_wait;

		[Header("References")]
		public TMPro.TMP_InputField		input_authcode;
		public TMPro.TMP_Text			label_version;
		public BuildProductName			build_setup;
		public Image					progressbar;
		
		
		public AudioSource				audio_source;

		public AspectRatioFitter aspect_ratio_fitter;

		[Header("General Settings")]
		public Button _bt_settings;
		public CanvasGroup content_settings;
		public InputSettings content_settings_behaviour;
		
		[Header("Input Settings")]
		public CanvasGroup content_input_choice;
		public Button _bt_input_choice;
		public Button _bt_mobile_input_choice_virtual_gamepad;
		public Button _bt_mobile_input_choice_gyro_arcade;
		public Button _bt_mobile_input_choice_gyro_shooter;
		public Button _bt_mobile_input_choice_gyro_racing;
		public Button _bt_mobile_input_choice_touch;
		public int ChosenInputMode; // using first 5 player_flags
		public Sprite icon_gamepad;
		public Sprite icon_gyro;
		public Sprite icon_gyro_driving;
		public Sprite icon_gyro_arcade;
		public Sprite icon_touch;
		
		
		[Space(10)]
		[Header("UI Settings")]
		public float		AnimationTime = 0.5f;

		public static Main Instance;

		public enum CameraModes
		{
			Unity,
			Native
		}

		[ContextMenu("Test NetworkDiscovery")]
		public void _test_network_discovery()
		{
			NetworkDiscoveryClient?.SendBroadcast();
		}

		private void Awake()
		{
			Instance = this;

			Log.Logger.DefaultLog = new Log.Logger(Path.Combine(Application.persistentDataPath, "log.txt"), true, true);
			Log.Logger.DefaultLog.onLog += (s) => UnityMainThreadDispatcher.Instance().Enqueue(() => Debug.Log(s));

			if (build_setup!=null)
				label_version?.SetText($"{build_setup.ProductVersion} ({build_setup.BuildVersion})");

            // Prevent the screen from sleeping
            UnityEngine.Screen.sleepTimeout = SleepTimeout.NeverSleep;
            
            // Set and Verify that Taptic engine is ENABLED
			Taptic.tapticOn = true;
			var tapticState = Taptic.tapticOn ? "Taptic Engine is ON" : "Taptic Engine is OFF";
            Debug.Log($"{tapticState}");

			// Client ID
			ClientID = SystemInfo.deviceUniqueIdentifier;

			// Network discovery
			try
			{
				if (TryGetComponent(out NetworkDiscoveryClient))
				{
					NetworkDiscoveryClient.onReceivedServerResponse.AddListener((NetworkDiscovery.DiscoveryInfo info) =>
					{
						// Received server response
						try
						{
							_gameserver_discovery = info;							

							// UnityMainThreadDispatcher.Instance().Enqueue(() => Debug.Log($"NetworkDiscoveryClient received server={info.EndPoint.Address.ToString()} port={info.GetGameServerPort()}"));
						}
						catch (Exception ex)
						{
							local_gameserver_address = null;
							UnityMainThreadDispatcher.Instance().Enqueue(() => Debug.LogException(ex));
						}

						// we received response from server
						// add it to list of servers, or connect immediately...
					});					
				}
			}
			catch(Exception ex)
			{
				Debug.LogException(ex);
			}

			// Setup first navigation
			content_login?.gameObject.SetActive(true);
			content_calibration?.gameObject.SetActive(false);
			calibrate_unity_content.gameObject.SetActive(false);

			SetAuthCode(AuthCode);			
		}

		private void Start()
		{
			Handheld.Vibrate();
			audio_source.PlayOneShot(sound_opening);
		}

		private void Update()
		{ 
			IEnumerator _discovery()
			{
				time_last_discovery = Time.time;
				_gameserver_discovery = null;

				try
				{
					NetworkDiscoveryClient?.SendBroadcast();
				}
				catch { }

				yield return new WaitForSecondsRealtime(DiscoveryWaitAfterBroadcast);

				try
				{
					if (_gameserver_discovery != null)
					{
						// TODO: handle more than one server/ip !!!

						if (local_gameserver_address == null)
							Debug.Log($"NetworkDiscoveryClient found server={_gameserver_discovery.EndPoint.Address.ToString()} port={_gameserver_discovery.GetGameServerPort()}");

						local_gameserver_address = _gameserver_discovery.EndPoint.Address.ToString();
						local_gameserver_port = _gameserver_discovery.GetGameServerPort();
					}
					else
					{
						if (GameServerAddress != null)
							Debug.Log($"NetworkDiscoveryClient lost server");

						local_gameserver_address = null;
						local_gameserver_port = 0;
					}					
				}
				catch (Exception ex)
				{
					Debug.LogException(ex);
					yield break;
				}
			}

			//FIXME: discovery always also when p2p is enabled?
			if (/*P2PStatus == P2PStates.None && */Time.time > (time_last_discovery + DiscoveryInterval))
			{
				// Discovery is started only if there is no custom server address
				if (!IsCustomServerAddressEnabled)
					StartCoroutine(_discovery());
			}

			bool bt_back_on = false;
		
			// Show / Hide contextual elements (buttons) depending 
			if (content_login.isActiveAndEnabled)
			{
				
			}

			if (content_calibration.isActiveAndEnabled)
			{
				bt_back_on = true;
			}

			if (content_controller.isActiveAndEnabled)
			{
				
			}

			_bt_back.gameObject.SetActive(bt_back_on);


			// special checks when player has joined
			if (content_controller.isActiveAndEnabled && mobile_controller.LastPlayerStatus != null)
			{
				// check that the player is connected and its status is playing, otherwise show the wait in queue
				// TODO: constants for states
				// TODO: also disable touch?
				if (mobile_controller.LastPlayerStatus.player_status != 3)
				{
					wait_queued.gameObject.SetActive(true);
					content_controller.interactable = false;
					content_controller.alpha = 0;
				}
				else
				{
					wait_queued.gameObject.SetActive(false);					
					content_controller.alpha = content_controller_target_alpha;
					content_controller.interactable = (content_controller.alpha > 0);
				}
			}
			else
			{
				wait_queued.gameObject.SetActive(false);				
				content_controller.alpha = content_controller_target_alpha;
				content_controller.interactable = (content_controller.alpha > 0);
			}
		}

		#region AuthCode

		public void SetAuthCode(string code)
		{
			input_authcode.SetTextWithoutNotify(code);
			AuthCode = code;
		}

		public void ResetAuthCode() => SetAuthCode("");

		public bool IsAuthCodeWellFormed => !string.IsNullOrEmpty(AuthCode) && AuthCode.Length == 6;

		#endregion

		#region Navigation

		// turns off everything and restores initial login page
		public void Home(Action custom_action=null)
		{
			content_calibration?.gameObject.SetActive(false);
			content_input_choice?.gameObject.SetActive(false);
			content_controller?.gameObject.SetActive(false);

			_bt_back.gameObject.SetActive(false);
			_bt_mobile_controller_disconnect.gameObject.SetActive(false);
			_bt_settings.gameObject.SetActive(true);
			_bt_input_choice.gameObject.SetActive(false);

			content_login?.gameObject.SetActive(true);
			content_login.DOFade(1, AnimationTime).From(0);

			SetupLogin();

			custom_action?.Invoke();
		}

		public void Back()
		{
			IEnumerator _back()
			{
				_bt_back.enabled = false;

				if (content_calibration.isActiveAndEnabled)
				{
					yield return content_calibration.DOFade(0, AnimationTime).From(1).WaitForCompletion();
					content_calibration?.gameObject.SetActive(false);
					content_calibration?.gameObject.SetActive(false);

					Home();
				}

				_bt_back.enabled = true;
			}

			Taptic.Selection();
			audio_source.PlayOneShot(sound_button_press);

			StartCoroutine(_back());
		}

		#endregion

		#region Login

		public void SetupLogin()
		{
			// if authcode is present, hide pad
			if (IsAuthCodeWellFormed)
			{
				_bt_login.gameObject.SetActive(true);
				_bt_login_clear.gameObject.SetActive(true);
				auth_code_pad.alpha = 0;
				auth_code_pad.interactable = false;
			}
			else
			{
				_bt_login.gameObject.SetActive(false);
				_bt_login_clear.gameObject.SetActive(false);
				auth_code_pad.alpha = 1;
				auth_code_pad.interactable = true;
			}
		}

		public void PinPadButtonInput(string s)
		{
			var code = input_authcode.text;

			if (!string.IsNullOrEmpty(code) && code.Length >= 6)
				return;
			
			Taptic.Selection();
			audio_source.PlayOneShot(sound_button_press);
			
			input_authcode.SetTextWithoutNotify(code + s);

			if (input_authcode.text.Length == 6)
			{
				content_login.interactable = false;

				ForceLocalAddress = false;

				StartCoroutine(LoginCR());
			}

		}

		public void bt_login_existing_authcode()
		{
			content_login.interactable = false;

			ForceLocalAddress = false;

			StartCoroutine(LoginCR());
		}

		public void bt_login_clear()
		{
			ResetAuthCode();
			SetupLogin();
		}

		IEnumerator LoginCR(CoroutineWaitForTaskResult result = null, float max_time = -1)
		{
			int check_authcode_result = -1;

			void _check_auth_code(string auth_code)
			{
				check_authcode_result = LicenseActivatorLib.CheckAuthCode(auth_code);	
			}

			_bt_choice_calibrate.interactable = false;

			Debug.Log($"Checking AuthCode: {input_authcode.text}");

			yield return progress.ShowCR(icon_progress_standard);

			yield return UnityUtilities.WaitForTask(() => _check_auth_code(input_authcode.text), result, max_time);			

			Debug.Log($"Checking AuthCode result={check_authcode_result}");

			if (check_authcode_result > 0)
			{				
				AuthCode = input_authcode.text;

				PlayerPrefs.SetString("auth_code", input_authcode.text);
				PlayerPrefs.Save();

				bool jump_to_contoller = false;

				// Configure depending on auth code
				switch (check_authcode_result)
				{
					case LicenseAuthCodeCheck_Response.Result_OK_NORMAL:
						_bt_choice_calibrate.gameObject.SetActive(false);
						// start preparing the p2p connection to be faster (it can fail)
						PrepareP2PConnection();
						jump_to_contoller = true;
						break;

					case LicenseAuthCodeCheck_Response.Result_OK_ADMIN:
						// Calibration, P2P is not useful
						StopP2PConnection();
						_bt_choice_calibrate.gameObject.SetActive(true);
						break;
				}

				if (jump_to_contoller)
				{
					yield return progress.HideCR(icon_ok, time: 1);
					Handheld.Vibrate();
					audio_source.PlayOneShot(sound_ok);

					// direct transition to gamepad
					bool p2p_ready = false;

					// already closes the progress dialog
					yield return P2PConnectionWaitDialog(
						onReady: () => p2p_ready = true,
						onFailed: () => p2p_ready = false);

					if (p2p_ready)
					{
						yield return content_login.DOFade(0, AnimationTime).From(1).WaitForCompletion();
						content_login?.gameObject.SetActive(false);
						ActivateGamepad();
					}
					else
					{
						ResetAuthCode();
					}					
				}
				else
				{
					// CALIBRATION

					_bt_settings.gameObject.SetActive(false);

					_bt_choice_calibrate.GetComponent<CanvasGroup>().alpha = 1;
					_bt_choice_calibrate.interactable = true;

					yield return content_login.DOFade(0, AnimationTime).From(1).WaitForCompletion();
					content_login?.gameObject.SetActive(false);
					Handheld.Vibrate();
					audio_source.PlayOneShot(sound_ok);
					yield return progress.HideCR(icon_ok, time: 1);

					content_calibration?.gameObject.SetActive(true);
					yield return content_calibration.DOFade(1, AnimationTime).From(0).WaitForCompletion();
				}
			}
			else
			{
				Taptic.Failure();
				audio_source.PlayOneShot(sound_error);
				yield return progress.HideCR(icon_error, msg: $"Authcode Error ({check_authcode_result})", time: 4);
				// msgbox.ShowModal($"Error: {check_authcode_result}", canvas_deactivate: content_login);

				ResetAuthCode();

				PlayerPrefs.SetString("auth_code", null);
				PlayerPrefs.Save();
			}

			content_login.interactable = true;
			_bt_choice_calibrate.interactable = true;
		}

		// Go straight to the local network 
		public void bt_local_network()
		{
			Taptic.Selection();
			audio_source.PlayOneShot(sound_button_press);

			ForceLocalAddress = true;
			if (string.IsNullOrEmpty(GameServerAddress))
			{
				msgbox.ShowModal("Local wifi server not found");
				return;
			}

			P2PStatus =  P2PStates.None;
			// StopP2PConnection();

			StartCoroutine(progress.ShowCR(icon_progress_standard));

			ActivateGamepad();
		}

		#endregion

		#region Connection

		// To speed things up the p2p connection is prepared as soon as the user logs in, can fail of course if there is no gameserver published
		Coroutine preparep2p_cr = null;
		public void PrepareP2PConnection()
		{
			IEnumerator _prepare_p2p_connection()
			{
				Debug.Log("P2P: start");

				p2p_gameserver_address = null;
				p2p_gameserver_port = 0;

				P2PStatus = P2PStates.Connecting;

				var p2p_server_info = new P2P_ServerInfo();

				yield return ConnectP2P_CR(AuthCode, p2p_server_info, P2PConnectionTimeout);

				Debug.Log($"P2P: p2p_server_info.ip={p2p_server_info.ip} status={p2p_server_info.connection_state}");

				if (!string.IsNullOrEmpty(p2p_server_info.ip))
				{
					p2p_gameserver_address = p2p_server_info.ip;
					p2p_gameserver_port = p2p_server_info.port;

					P2PStatus = P2PStates.Ready;
				}
				else
				{
					p2p_gameserver_address = null;
					p2p_gameserver_port = 0;

					P2PStatus = P2PStates.Failed;
				}

				preparep2p_cr = null;
			}

			if (preparep2p_cr != null)
				return;

			if (string.IsNullOrEmpty(AuthCode))
			{
				Debug.LogError("Cannot setup a P2P connection without AuthCode");
				return;
			}

			// TEST: honour a current available P2P connection
			if (P2PStatus == P2PStates.Ready &&
				P2PInstance.CurrentConnection != null && P2PInstance.CurrentConnection.status == Tabula.P2P.P2P.ClientData.State.Connected)
			{
				// tunnelling should be ready
				return;
			}
			else
			{
				// Stop curren connection and start another
				StopP2PConnection();
				preparep2p_cr = StartCoroutine(_prepare_p2p_connection());
			}
		}

		public void StopP2PConnection()
		{
			Task.Run(() => P2PInstance.StopClient(p2p_client_data.matchMakerInstance));
			p2p_gameserver_address = null;
			p2p_gameserver_port = 0;
			P2PStatus = P2PStates.None;
		}

		//NOTE: no more login button, now the pinpad auto-logins
		/*
		public void bt_login()
        {
			IEnumerator _login()
			{
				yield return _bt_login.transform.DOScale(1, 0.2f).From(0.8f).WaitForCompletion();

				yield return LoginCR();
			}

			audio_source.PlayOneShot(sound_button_press);

			StartCoroutine(_login());
        }
		*/
		
		public IEnumerator P2PConnectionWaitDialog(Action onReady=null, Action onFailed=null)
		{
			switch (P2PStatus)
			{
				case P2PStates.Connecting:

					// show progress
					yield return progress.ShowCR(icon_progress_standard);

					while (P2PStatus == P2PStates.Connecting)
						yield return null;

					switch (P2PStatus)
					{
						case P2PStates.Ready:
							yield return progress.HideCR(icon_ok, time: 1);
							onReady?.Invoke();
							break;

						case P2PStates.Failed:
														
							yield return progress.HideCR(icon_error, msg: p2p_client_data!=null ? $"P2P: {p2p_client_data?.status.ToString()}" : null);
							//yield return msgbox.ShowModalCR("Connection failed. Please retry.");
							//PrepareP2PConnection();
							onFailed?.Invoke();
							yield break;
					}
					break;

				case P2PStates.None:	// no connection setup
				case P2PStates.Failed:

					//yield return msgbox.ShowModalCR("Connection failed. Please retry.");
					//PrepareP2PConnection();
					onFailed?.Invoke();
					yield break;


				case P2PStates.Ready:
					// OK!
					onReady?.Invoke();
					break;
			}
		}

		// attempts the connection to the P2P with the current auth-code

		public class P2P_ServerInfo
		{
			public string ip;
			public int port;

			public Tabula.P2P.P2P.ClientData.State connection_state;
			public CoroutineWaitForTaskResult cr_result = new CoroutineWaitForTaskResult();
		}

		public IEnumerator ConnectP2P_CR(string auth_code, P2P_ServerInfo p2p_server_info, float max_time = -1)
		{
			p2p_client_data = null;
			bool client_result = false;

			Task.Run(() => P2PInstance.StartClient(auth_code,
				async (client_data) =>
				{
					p2p_client_data = client_data;
					client_result = true;
				}));

			if (max_time == -1)
				max_time = float.MaxValue;

			var t_start = Time.time;
			while (!client_result && p2p_client_data == null && Time.time < t_start + max_time)
				yield return null;

			if (p2p_client_data == null)
			{
				p2p_server_info.connection_state = Tabula.P2P.P2P.ClientData.State.Timeout;
				p2p_server_info.ip = null;
				yield break;
			}

			p2p_server_info.connection_state = p2p_client_data.status;

			if (p2p_client_data.status == Tabula.P2P.P2P.ClientData.State.Connected)
			{
				p2p_server_info.ip = p2p_client_data.ip;
				p2p_server_info.port = p2p_client_data.port;
			}
			else
			{
				p2p_server_info.ip = null;
			}

		}

		// User explicit disconnect on long press
		public void bt_mobile_controller_disconnect()
		{
			DeactivateGamepad();
		}

		#endregion

		#region Input Choice

		private void Configure_content_input_choice(int playerFlag)
		{
			// enables/disables the available input types on UI:

			_bt_mobile_input_choice_virtual_gamepad.gameObject.SetActive((playerFlag & PlayerControllerMessage.Flags_GamePad) != 0);
			_bt_mobile_input_choice_gyro_arcade.gameObject.SetActive((playerFlag & PlayerControllerMessage.Flags_GyroArcade) != 0);
			_bt_mobile_input_choice_gyro_shooter.gameObject.SetActive((playerFlag & PlayerControllerMessage.Flags_GyroShooter) != 0);
			_bt_mobile_input_choice_gyro_racing.gameObject.SetActive((playerFlag & PlayerControllerMessage.Flags_GyroDriving) != 0);
			_bt_mobile_input_choice_touch.gameObject.SetActive((playerFlag & PlayerControllerMessage.Flags_Touch) != 0);
		}		

		#region Input Choice

		public List<int> CheckInputTypes(int playerFlags)
		{
			List<int> availableInputs = new List<int>();
            if ((mobile_controller.PlayerJoinResult.player_flags & PlayerControllerMessage.Flags_GamePad) != 0)
            {
				availableInputs.Add(Input_Gamepad);
            }
            if ((mobile_controller.PlayerJoinResult.player_flags & PlayerControllerMessage.Flags_GyroShooter) != 0)
            {
                availableInputs.Add(Input_GyroShooter);
            }
            if ((mobile_controller.PlayerJoinResult.player_flags & PlayerControllerMessage.Flags_GyroArcade) != 0)
            {
                availableInputs.Add(Input_GyroArcade);
            }
            if ((mobile_controller.PlayerJoinResult.player_flags & PlayerControllerMessage.Flags_GyroDriving) != 0)
            {
                availableInputs.Add(Input_GyroDriving);
            }
            if ((mobile_controller.PlayerJoinResult.player_flags & PlayerControllerMessage.Flags_Touch) != 0)
            {
                availableInputs.Add(Input_Touch);
            }

            return availableInputs;
        }				

		public void OpenInputChoice()
		{
			// old code with anims.. too slow
			/*
			IEnumerator _input_choice()
			{
				if (!content_input_choice.isActiveAndEnabled)
				{
                    Configure_content_input_choice(mobile_controller.PlayerJoinResult.player_flags);
                    gyro_controller.gameObject.SetActive(false);
                    stick_controller.gameObject.SetActive(false);
                    touch_controller.gameObject.SetActive(false);
                    StickController.Enabled = false;
                    content_controller.interactable = false;
                    yield return _bt_input_choice.GetComponent<Image>().DOFade(0, AnimationTime / 2).From(1);
                    yield return content_controller.DOFade(0, AnimationTime).From(1).WaitForCompletion();

                    yield return content_input_choice.DOFade(1, AnimationTime).From(0).WaitForCompletion();
                    content_input_choice.gameObject.SetActive(true);

				} 
				else if (content_input_choice.isActiveAndEnabled)
				{
					if (CheckInputTypes(mobile_controller.PlayerJoinResult.player_flags).Count > 1)
						yield return _bt_input_choice.GetComponent<Image>().DOFade(1, AnimationTime / 2).From(0);

					yield return content_input_choice.DOFade(0, AnimationTime).From(1).WaitForCompletion();
					content_input_choice.gameObject.SetActive(false);
					
					yield return content_controller.DOFade(1, AnimationTime).From(0).WaitForCompletion();

					content_controller.interactable = true;

                    switch (chosen_input_mode)
                    {
                        case 0:
							StickController.Enabled = true;
							stick_controller.gameObject.SetActive(true);
                            break;
                        case 1:
	                        touch_controller.gameObject.SetActive(true);
                            break;
                        case 2:
                            // GYRO ARCADE: not implemented
                            break;
                        case 3:
                            StickController.Enabled = false;
							gyro_controller.gameObject.SetActive(true);
                            break;
                        case 4:
                            // GYRO DRIVING: not implemented
                            break;
                        default:
                            break;
                    }
				}
			}
			*/

			Taptic.Selection();
			audio_source.PlayOneShot(sound_button_press);

			// StartCoroutine(_input_choice());

			if (content_input_choice.isActiveAndEnabled)
				return;

			Configure_content_input_choice(mobile_controller.PlayerJoinResult.player_flags);

			// reset all
			gyro_controller.gameObject.SetActive(false);
			stick_controller.gameObject.SetActive(false);
			touch_controller.gameObject.SetActive(false);
			StickController.Enabled = false;

			_bt_input_choice.gameObject.SetActive(false);
			_bt_mobile_controller_disconnect.gameObject.SetActive(false);
			_bt_settings.gameObject.SetActive(false);

			content_controller_target_alpha = 0;

			content_input_choice.gameObject.SetActive(true);
		}

		public void CloseInputChoice(int chosen_input)
		{
			ChosenInputMode = chosen_input;

			if (CheckInputTypes(mobile_controller.PlayerJoinResult.player_flags).Count > 1)
				_bt_input_choice.gameObject.SetActive(true);

			content_input_choice.gameObject.SetActive(false);

			content_controller_target_alpha = 1;

			ConfigureInput();

			_bt_settings.gameObject.SetActive(true);
			_bt_mobile_controller_disconnect.gameObject.SetActive(true);

		}

		#endregion

		#endregion

		#region Settings

		public void OpenSettings()
		{
			Taptic.Selection();
			audio_source.PlayOneShot(sound_button_press);

			// Check if we are in initial panel (content_login)
			if (content_login.isActiveAndEnabled)
			{
				ChosenInputMode = -1;
				content_login.gameObject.SetActive(false);
			}
			
			gyro_controller.gameObject.SetActive(false);
			stick_controller.gameObject.SetActive(false);
			touch_controller.gameObject.SetActive(false);
			StickController.Enabled = false;

			_bt_input_choice.gameObject.SetActive(false);
			_bt_mobile_controller_disconnect.gameObject.SetActive(false);
			_bt_settings.gameObject.SetActive(false);

			content_controller_target_alpha = 0;

			content_settings.gameObject.SetActive(true);
		}

		public void CloseSettings()
		{
			content_settings.gameObject.SetActive(false);
			_bt_settings.gameObject.SetActive(true);

			if (ChosenInputMode >= 0)
			{
				_bt_mobile_controller_disconnect.gameObject.SetActive(true);
				if (CheckInputTypes(mobile_controller.PlayerJoinResult.player_flags).Count > 1)
					_bt_input_choice.gameObject.SetActive(true);
			}

			content_controller_target_alpha = 1;

			ConfigureInput();

			PlayerPrefs.Save();
		}

		private void ConfigureInput()
		{
			// Enable available Buttons (A,B,X,Y) or single button
			bool _has_button_flag(int button_flag) => 
				(mobile_controller.PlayerJoinResult!=null ? mobile_controller.PlayerJoinResult.player_flags & button_flag : 0) != 0;

			// reset ALL controllers
			gyro_controller.gameObject.SetActive(false);
			stick_controller.gameObject.SetActive(false);
			touch_controller.gameObject.SetActive(false);
			StickController.Enabled = false;

			// reset ALL buttons
			mobile_controller.ButtonNorth.gameObject.SetActive(false);
			mobile_controller.ButtonSouth.gameObject.SetActive(false);
			mobile_controller.ButtonEast.gameObject.SetActive(false);
			mobile_controller.ButtonWest.gameObject.SetActive(false);
			mobile_controller.ButtonSingleLeft.gameObject.SetActive(false);
			mobile_controller.ButtonSingleRight.gameObject.SetActive(false);
			mobile_controller.ButtonDouble1.gameObject.SetActive(false);
			mobile_controller.ButtonDouble2.gameObject.SetActive(false);

			// reset special layouts
			mobile_controller.ShooterLayout.gameObject.SetActive(false);
			mobile_controller.DrivingLayout.gameObject.SetActive(false);

			// FIRST: configure the buttons, that can live with different input modes

			if (ChosenInputMode == Input_GyroDriving) 
			{
				// special, only two buttons
				mobile_controller.ButtonSingleLeft.gameObject.SetActive(true);
				mobile_controller.ButtonSingleLeft.GetComponentInChildren<TMPro.TMP_Text>().SetText("BRAKE");

				mobile_controller.ButtonSingleRight.gameObject.SetActive(true);
				mobile_controller.ButtonSingleRight.GetComponentInChildren<TMPro.TMP_Text>().SetText("SPEED");
			}
			else
			{
				// standard buttons

				int num_buttons = 0;
				if (_has_button_flag(PlayerControllerMessage.Flags_Gamepad_ButtonNorth))
				{
					mobile_controller.ButtonNorth.gameObject.SetActive(true);
					num_buttons++;
				}
				else
					mobile_controller.ButtonNorth.gameObject.SetActive(false);

				if (_has_button_flag(PlayerControllerMessage.Flags_Gamepad_ButtonSouth))
				{
					mobile_controller.ButtonSouth.gameObject.SetActive(true);
					num_buttons++;
				}
				else
					mobile_controller.ButtonSouth.gameObject.SetActive(false);

				if (_has_button_flag(PlayerControllerMessage.Flags_Gamepad_ButtonEast))
				{
					mobile_controller.ButtonEast.gameObject.SetActive(true);
					num_buttons++;
				}
				else
					mobile_controller.ButtonEast.gameObject.SetActive(false);

				if (_has_button_flag(PlayerControllerMessage.Flags_Gamepad_ButtonWest))
				{
					mobile_controller.ButtonWest.gameObject.SetActive(true);
					num_buttons++;
				}
				else
					mobile_controller.ButtonWest.gameObject.SetActive(false);

				if (num_buttons == 1)
				{
					mobile_controller.ButtonNorth.gameObject.SetActive(false);
					mobile_controller.ButtonSouth.gameObject.SetActive(false);
					mobile_controller.ButtonEast.gameObject.SetActive(false);
					mobile_controller.ButtonWest.gameObject.SetActive(false);

					// just enable single big button
					mobile_controller.ButtonSingleRight.gameObject.SetActive(true);

					// TODO: name of the big button depending on game?
					mobile_controller.ButtonSingleRight.GetComponentInChildren<TMPro.TMP_Text>().SetText("ACTION");
				}
				else if (num_buttons == 2)
				{
					mobile_controller.ButtonNorth.gameObject.SetActive(false);
					mobile_controller.ButtonSouth.gameObject.SetActive(false);
					mobile_controller.ButtonEast.gameObject.SetActive(false);
					mobile_controller.ButtonWest.gameObject.SetActive(false);

					// just enable 2 single bigger buttons
					mobile_controller.ButtonDouble1.gameObject.SetActive(true);
					mobile_controller.ButtonDouble2.gameObject.SetActive(true);

					mobile_controller.ButtonDouble1.gameObject.GetComponentInChildren<TMPro.TMP_Text>().SetText("A");
					mobile_controller.ButtonDouble2.gameObject.GetComponentInChildren<TMPro.TMP_Text>().SetText("B");
				}

			}

			// SECOND: enable right controllers

			switch (ChosenInputMode)
			{
				case Input_Gamepad:
					StickController.Enabled = true;
					stick_controller.gameObject.SetActive(true);
					_bt_input_choice.gameObject.GetComponent<Image>().sprite = icon_gamepad;
					//_bt_input_choice.gameObject.GetComponent<Image>().color = color_icon_gamepad;
					break;

				case Input_Touch:
					touch_controller.gameObject.SetActive(true);
					_bt_input_choice.gameObject.GetComponent<Image>().sprite = icon_touch;
					//_bt_input_choice.gameObject.GetComponent<Image>().color = color_icon_touch;
					break;

				case Input_GyroArcade:
					// GYRO ARCADE: not implemented
					_bt_input_choice.gameObject.GetComponent<Image>().sprite = icon_gyro;
					SensorController.Instance.SetControllerType(ControllerType.ArcadeTypeB);
					//_bt_input_choice.gameObject.GetComponent<Image>().color = color_icon_gyro_arcade;
					break;

				case Input_GyroShooter:
					mobile_controller.ShooterLayout.gameObject.SetActive(true);
					gyro_controller.gameObject.SetActive(true);
					SensorController.Instance.SetControllerType(ControllerType.Shooter);
					_bt_input_choice.gameObject.GetComponent<Image>().sprite = icon_gyro;
					//_bt_input_choice.gameObject.GetComponent<Image>().color = color_icon_gyro_shooter;
					break;

				case Input_GyroDriving:
					mobile_controller.DrivingLayout.gameObject.SetActive(true);
					gyro_controller.gameObject.SetActive(true);
					SensorController.Instance.SetControllerType(ControllerType.Racing);
					_bt_input_choice.gameObject.GetComponent<Image>().sprite = icon_gyro_driving;
					//_bt_input_choice.gameObject.GetComponent<Image>().color = color_icon_gyro_driving;
					break;

				default:
					// Restore initial panel (content_login)
					content_login.gameObject.SetActive(true);
					break;
			}

			// enable service buttons
			_bt_mobile_controller_disconnect.gameObject.SetActive(true);
			_bt_settings.gameObject.SetActive(true);
		}

		#endregion

		#region Controller

		public void ActivateGamepad()
		{
			IEnumerator _activate_gamepad()
			{
				mobile_controller.Setup();

				_bt_settings.gameObject.SetActive(false);
				mobile_controller.ButtonSingleRight.gameObject.SetActive(false);

				// fade control (can arrive from choice or main screen)
				if (content_calibration.gameObject.activeInHierarchy)
				{
					yield return content_calibration.DOFade(0, AnimationTime).From(1).WaitForCompletion();
					content_calibration.gameObject.SetActive(false);
				}
				else if (content_login.gameObject.activeInHierarchy)
				{
					yield return content_login.DOFade(0, AnimationTime).From(1).WaitForCompletion();
					content_login.gameObject.SetActive(false);
				}

				// Connect!
				// NOTE: content must be active first to initialize PMClient etc..
				content_controller.gameObject.SetActive(true);
				content_controller.alpha = 0;

				// Setup mobile_controller events
				bool retry_connection = false;

				// NOTE: this will NOT be called on the Unity main thread
				mobile_controller.OnStatusChanged = (s, o, msg) =>
				{
					Debug.Log($"MobileController: OnStatusChanged() status={s} msg={msg}");

					switch (s)
					{
						case MobileController.ConnectionStates.OnConnectionStarted:
							retry_connection = false;
							//progress.Hide();
							break;

						case MobileController.ConnectionStates.OnConnectionFailed:
							retry_connection = false;
							progress.Hide();
							dialog_icon.Show(dialog_config_connect_fail, msg);
							break;

						case MobileController.ConnectionStates.OnConnectionTimedOut:
							retry_connection = false;
							progress.Hide();
							dialog_icon.Show(dialog_config_connect_fail, msg);
							break;

						case MobileController.ConnectionStates.OnJoinSuccesful:
							retry_connection = false;
							progress.Hide();
							//dialog_icon.Show(dialog_config_gamepad);
							break;

						case MobileController.ConnectionStates.OnJoinFailed:
							retry_connection = false;
							progress.Hide();
							dialog_icon.Show(dialog_config_connect_fail, msg);
							//msgbox.ShowModal("Join failed! Please retry.");
							break;

						case MobileController.ConnectionStates.OnJoinFailedCannotJoinNoRetry:
						case MobileController.ConnectionStates.OnJoinFailedException:
							retry_connection = false;
							progress.Hide();
							dialog_icon.Show(dialog_config_connect_fail, msg);
							//msgbox.ShowModal("Join error!");
							break;

						case MobileController.ConnectionStates.OnJoinFailedCannotJoin:
							retry_connection = true;
							//dialog_icon.Show(dialog_config_connect_wait);
							break;

						case MobileController.ConnectionStates.OnLeaveSuccesful:
							dialog_icon.Show(dialog_config_kill, msg);
							break;

						case MobileController.ConnectionStates.OnLeaveFailed:
							msgbox.ShowModal("Player left ERROR");
							break;

						case MobileController.ConnectionStates.OnServerDisconnect:

							StopP2PConnection();
							// server disconnected (failed keepalive), we can be anywhere, go back home
							UnityMainThreadDispatcher.Instance().Enqueue(() => Home(custom_action: () => dialog_icon.Show(dialog_config_kill, msg)));
		
							break;
					}
				};

				float time_connection = Time.time;
				int connection_try = 1;

			retry_connection_label:

				// let's leave the events handle the resulting messageboxes 
				Debug.Log($"Connection try:{connection_try} retry:{retry_connection}");

				// If a connection is set as retry, it will just attempt the join RPC call, assuming the client is already connected
				yield return mobile_controller.ConnectCR(
					SystemInfo.deviceUniqueIdentifier, 
					GameServerAddress, 
					GameServerPort,
					join_only: retry_connection);
				
				if (!mobile_controller.IsConnected)
				{
					// if we cannot join this time, retry for some seconds
					if (retry_connection && (Time.time < time_connection + MaxJoinRetryTime))
					{
						progress.Show(icon_connect_wait, fade: false);
						connection_try++;
						yield return new WaitForSecondsRealtime(2);
						goto retry_connection_label;
					}					

					progress.Hide();
					retry_connection = false;

					//back to the start, should retain code login
					content_controller.gameObject.SetActive(false);
					content_login.gameObject.SetActive(true);

					ResetAuthCode();
					StopP2PConnection();

					_bt_mobile_controller_disconnect.gameObject.SetActive(false);
					_bt_input_choice.gameObject.SetActive(false);
					_bt_settings.gameObject.SetActive(true);

					yield return content_login.DOFade(1, AnimationTime).From(0).WaitForCompletion();

					SetupLogin();					
				}
				else
				{
					// CONNECT OK
					retry_connection = false;

					// Setup events for disconnection requested by server
					mobile_controller.OnServerAskedDisconnect = () =>
					{
						// flag this as a server-initiated disconnection, so that we don't call NetworkPlayerLeave

						// This is treated as a death visually
						dialog_icon.Show(dialog_config_kill);

						DeactivateGamepad();

						// vibro
						Handheld.Vibrate();
					};

					mobile_controller.OnPlayerKilled = (int remaining_lives) =>
					{
						// report kill with effect
						dialog_icon.Show(dialog_config_kill, $"Lives: {remaining_lives}");

						// vibro
						Handheld.Vibrate();
					};					

					// Opens automatically the content_input_choice using the PlayerJoinResult.player_flags to show only the available input on UI
					// Only if available input type are more than 1
					var inputTypes = CheckInputTypes(mobile_controller.PlayerJoinResult.player_flags);
					
					if (inputTypes.Count > 1)
					{
						OpenInputChoice();
					}
					else 
					{
						yield return content_controller.DOFade(1, AnimationTime).From(0).WaitForCompletion();

                        ChosenInputMode = inputTypes.First();
						ConfigureInput();
					}					
				}				
			}

			StartCoroutine(_activate_gamepad());
		}

		// Will also disconnect
		public void DeactivateGamepad()
		{
			IEnumerator _deactivate_gamepad()
			{				
				yield return mobile_controller.DisconnectCR();

				// TODO: do not really disconnect?
				// StopP2PConnection();

				if (mobile_controller.IsConnected)
				{
					// yield return msgbox.ShowModalCR("Disconnecton failed");
					// but let's go on anyway
				}

				mobile_controller.OnStatusChanged = null;
				mobile_controller.OnPlayerKilled = null;
				mobile_controller.OnServerAskedDisconnect = null;

				// back to the start, should stay logged!
				// NOTE: server info must be cleared when we go back to the login, but we should hang on here
				content_controller.gameObject.SetActive(false);
				content_login.gameObject.SetActive(true);
                _bt_input_choice.gameObject.SetActive(false);
				_bt_settings.gameObject.SetActive(true);

				//ResetAuthCode();
				StopP2PConnection();

				input_authcode.SetTextWithoutNotify(AuthCode);
				_bt_mobile_controller_disconnect.gameObject.SetActive(false);
				yield return content_login.DOFade(1, AnimationTime).From(0).WaitForCompletion();

				SetupLogin();
			}

			StartCoroutine(_deactivate_gamepad());
		}

		#endregion

		#region Auto Calibration choice

		Coroutine choice_calibrate_cr = null;
		public void bt_choice_calibrate()
		{
			IEnumerator _calibrateCR()
			{
				_bt_choice_calibrate.interactable = false;
				yield return _bt_choice_calibrate.transform.DOScale(1, AnimationTime).From(0.8f).WaitForCompletion();
				
				if (CameraMode == CameraModes.Unity)
				{
					// Camera UI with Overlay is used only in Unity mode
					yield return content_calibration.DOFade(0, AnimationTime).From(1).WaitForCompletion();
					content_calibration?.gameObject.SetActive(false);
					calibrate_unity_content?.gameObject.SetActive(true);
					yield return calibrate_unity_content.DOFade(1, AnimationTime).From(0).WaitForCompletion();

					// auth
					yield return Application.RequestUserAuthorization(UserAuthorization.WebCam);

					// Init the camera
					yield return InitCameraCR(WebCamKind.WideAngle);
				}
				else
				{
					// both the auth, and starting the picture taking process
					yield return InitCameraNativeCR();

					yield return TakePhotoNativeCR();
					
					yield return UploadLastImageCR();
				}

				_bt_choice_calibrate.interactable = true;

				choice_calibrate_cr = null;
			}

			if (choice_calibrate_cr != null)
				return;

			Taptic.Selection();
			audio_source.PlayOneShot(sound_button_press);

			choice_calibrate_cr = StartCoroutine(_calibrateCR());
		}

		#region Unity Calibration (UI)

		// calibrate is used only in Unity camera mode
		public void bt_calibrate_unity_confirm()
		{
			IEnumerator _calibrate()
			{
				yield return _bt_calibrate_unity_confirm.transform.DOScale(1, 0.2f).From(0.8f).WaitForCompletion();

				yield return TakePhotoCR();

				yield return UploadLastImageCR();
			}

			if (CameraMode != CameraModes.Unity)
				return;

			Taptic.Selection();
			audio_source.PlayOneShot(sound_button_press);

			StartCoroutine(_calibrate());
		}		

		public void bt_calibrate_unity_abort()
		{
			IEnumerator _back()
			{
				yield return _bt_calibrate_unity_abort.transform.DOScale(1, 0.2f).From(0.8f).WaitForCompletion();

				yield return calibrate_unity_content.DOFade(0, AnimationTime).From(1).WaitForCompletion();
				calibrate_unity_content?.gameObject.SetActive(false);
				content_calibration?.gameObject.SetActive(true);
				yield return content_calibration.DOFade(1, AnimationTime).From(0).WaitForCompletion();
			}

			Taptic.Selection();
			audio_source.PlayOneShot(sound_button_press);

			webcamTexture?.Stop();

			StartCoroutine(_back());
		}

		#endregion

		#endregion

		#region Take/Upload status

		private byte[] image_bytes_jpg;
		private string image_path;
		private int    image_upload_result;

		private void ResetPhotoStatus()
		{
			image_bytes_jpg = null;
			image_path = null;
			image_upload_result = -1;
		}

		private IEnumerator UploadLastImageCR()
		{
			if (image_bytes_jpg == null)
				yield break;

			// audio_source.PlayOneShot(sound_progress);
			yield return progress.ShowCR(icon_progress_calibration);

			Debug.Log($"Uploading Image");

			yield return UnityUtilities.WaitForTask(()
				=> image_upload_result = LicenseActivatorLib.CalibrationImageUpload(AuthCode, image_path));

			Debug.Log($"Uploaded Image: {image_upload_result}");

			if (image_upload_result != CalibrationImageUpload_Response.Result_OK)
			{
				Taptic.Failure();
				audio_source.PlayOneShot(sound_error);
				yield return progress.HideCR();
				// msgbox.ShowModal($"Error uploading image, please retry");
			}
			else
			{
				Handheld.Vibrate();
				audio_source.PlayOneShot(sound_ok);
				yield return progress.HideCR(icon_ok, time: 1);
			}
		}

		#endregion

		#region Unity Camera

		public void InitCamera()
		{
			StartCoroutine(InitCameraCR(WebCamKind.WideAngle));
		}
		
		public IEnumerator InitCameraCR(WebCamKind cameraKind)
		{
			bool _find_closest(Resolution[] resolutions, int desiredWidth, int desiredHeight, ref Resolution closestResolution)
			{
				bool found = false;
				int closestDifference = int.MaxValue;

				closestResolution = default;

				foreach (var resolution in resolutions)
				{
					int difference;
					if (desiredHeight != 0)
					{
						difference = Math.Abs(resolution.width - desiredWidth) + Math.Abs(resolution.height - desiredHeight);
					}
					else
					{
						difference = Math.Abs(resolution.width - desiredWidth);
					}

					if (difference < closestDifference)
					{
						found = true;
						closestDifference = difference;
						closestResolution = resolution;
					}
				}

				return found;
			}

			yield return new WaitForEndOfFrame();

			WebCamDevice[] devices = WebCamTexture.devices;
			if (devices.Length == 0)
			{
				Debug.LogError("No camera device found");
				yield break;
			}


			for (int i = 0; i < devices.Length; i++)
			{
				if (!devices[i].isFrontFacing
					&& devices[i].kind == cameraKind
					&& devices[i].isAutoFocusPointSupported)
				{

					if (!_find_closest(devices[i].availableResolutions, 1600, 0, ref webcam_resolution))
						continue;


					// get the best for full-hd ?
					Debug.Log("Creating webtexture");
					webcamTexture = new WebCamTexture(devices[i].name, webcam_resolution.width, webcam_resolution.height);

					// NOTE: Assign the texture before playing to avoid crashes!
					if (webcamTexture != null)
					{
						ui_camera_image.texture = webcamTexture;
						ui_camera_image.material.mainTexture = webcamTexture;
					}

					aspect_ratio_fitter.aspectRatio = (float) webcam_resolution.width / (float)webcam_resolution.height;
				}
			}

			if (webcamTexture == null)
			{
				Debug.LogError("No camera with AutoFocus of type " + cameraKind + " found");
				yield break;
			}

			//Debug.Log("Webtexture Play");
			webcamTexture.Play();

			// Assign texture to a temporary quad and destroy it after 5 seconds
		}

		private IEnumerator TakePhotoCR()
		{
			Texture2D _resize_image(Texture2D sourceImage, int desiredWidth, int desiredHeight)
			{
				RenderTexture rt = RenderTexture.GetTemporary(desiredWidth, desiredHeight);
				RenderTexture.active = rt;

				UnityEngine.Graphics.Blit(sourceImage, rt);

				Texture2D resizedTexture = new Texture2D(desiredWidth, desiredHeight);
				resizedTexture.ReadPixels(new Rect(0, 0, desiredWidth, desiredHeight), 0, 0);
				resizedTexture.Apply();

				RenderTexture.active = null;
				RenderTexture.ReleaseTemporary(rt);

				return resizedTexture;
			}

			ResetPhotoStatus();

			yield return new WaitForEndOfFrame();

			Texture2D texture = new Texture2D(webcamTexture.width, webcamTexture.height);
			texture.SetPixels(webcamTexture.GetPixels());
			texture.Apply();

			int desiredWidth = image_width;
			int desiredHeight = (int)((float)image_width * ((float) texture.height / (float)texture.width));

			var resized_texture = _resize_image(texture, desiredWidth, desiredHeight);
			image_bytes_jpg = resized_texture.EncodeToJPG(95);
			Destroy(resized_texture);
			
			string fileName = "calibration.jpg";
			image_path = Path.Combine(Application.persistentDataPath, fileName);

			if (File.Exists(image_path))
				File.Delete(image_path);

			File.WriteAllBytes(image_path, image_bytes_jpg);
		}

		#endregion

		#region Native Camera

		// Uses Native Plugin

		private IEnumerator InitCameraNativeCR()
		{
			Debug.Log("PMController: NativeCamera.CheckPermision()");
			var permission = NativeCamera.CheckPermission(true);
			Debug.Log($"PMController: NativeCamera.CheckPermision() = {permission}");

			if (permission != NativeCamera.Permission.Granted)
			{
				Debug.Log("PMController: NativeCamera.RequestPermission()");
				permission = NativeCamera.RequestPermission(true);
				Debug.Log($"PMController: NativeCamera.RequestPermission() = {permission}");

				if (permission != NativeCamera.Permission.Granted)
				{
					yield break;
				}
			}
		}

		private IEnumerator TakePhotoNativeCR()
		{
			int native_take_result = 0;

			ResetPhotoStatus();

			NativeCamera.Permission permission = NativeCamera.TakePicture((path) =>
				{
					Debug.Log("Image path: " + path);
					if (path == null)
					{
						native_take_result = -1;
						return;
					}
				
					// Create a Texture2D from the captured image
					Texture2D texture = NativeCamera.LoadImageAtPath(path, maxSize: image_width, generateMipmaps: false, markTextureNonReadable: false);
					if (texture == null)
					{
						Debug.Log("Couldn't load texture from " + path);
						native_take_result = -2;
						return;
					}

					image_bytes_jpg = texture.EncodeToJPG(95);
					Destroy(texture);

					string fileName = "calibration.jpg";
					image_path = Path.Combine(Application.persistentDataPath, fileName);

					if (File.Exists(image_path))
						File.Delete(image_path);

					File.WriteAllBytes(image_path, image_bytes_jpg);

					native_take_result = 1;


				}, image_width, true, NativeCamera.PreferredCamera.Rear);

			while (native_take_result == 0)
				yield return null;
		}

		#endregion

		// Cleanup P2P instance when the object is destroyed
		private void OnDestroy()
		{
			_p2pInstance?.Dispose();
			_p2pInstance = null;
		}

	}



}
